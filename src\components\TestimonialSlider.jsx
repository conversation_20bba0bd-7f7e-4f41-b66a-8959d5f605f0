'use client';

import React, { useState, useEffect } from 'react';


const TestimonialSlider = ({ testimonials }) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isVisible, setIsVisible] = useState(true);

  // Auto-slide every 5 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      setIsVisible(false);
      setTimeout(() => {
        setCurrentIndex((prev) => (prev + 1) % testimonials.length);
        setIsVisible(true);
      }, 300);
    }, 5000);

    return () => clearInterval(interval);
  }, [testimonials.length]);

  const goToSlide = (index) => {
    if (index === currentIndex) return;
    
    setIsVisible(false);
    setTimeout(() => {
      setCurrentIndex(index);
      setIsVisible(true);
    }, 300);
  };

  const currentTestimonial = testimonials[currentIndex];

  return (
    <div className="relative max-w-4xl mx-auto">
      {/* Main testimonial */}
      <div className={`transition-all duration-500 ${isVisible ? 'opacity-100' : 'opacity-0'}`}>
        <div className="text-center relative">
          {/* Subtelny wzór mandali w tle */}
          <div className="absolute inset-0 opacity-[0.03] bg-gradient-to-r from-amber-50 via-orange-50 to-amber-50" />
          
          {/* Balijskie Quote marks */}
          <div className="text-8xl text-amber-600/20 mb-6 font-serif leading-none">
            ❝
          </div>
          
          {/* Quote text */}
          <p className="text-gray-700 text-lg md:text-xl leading-relaxed mb-8 italic max-w-3xl mx-auto relative z-10">
            {currentTestimonial.quote}
          </p>
          
          {/* Author z flagą kraju */}
          <div className="flex items-center justify-center gap-2 relative z-10">
            {currentTestimonial.flag && (
              <span className="text-lg">{currentTestimonial.flag}</span>
            )}
            <p className="text-gray-500 font-light">
              <span className="font-normal">{currentTestimonial.author}</span>
              {currentTestimonial.location && (
                <span>, {currentTestimonial.location}</span>
              )}
            </p>
          </div>
        </div>
      </div>
      
      {/* Dots navigation */}
      <div className="flex justify-center mt-12 space-x-3">
        {testimonials.map((_, index) => (
          <button
            key={index}
            onClick={() => goToSlide(index)}
            className={`w-3 h-3 rounded-full transition-all duration-300 ${
              index === currentIndex
                ? 'bg-gray-400 scale-110'
                : 'bg-gray-200 hover:bg-gray-300'
            }`}
            aria-label={`Przejdź do opinii ${index + 1}`}
          />
        ))}
      </div>
      
      {/* Progress bar (subtle) */}
      <div className="mt-8 max-w-md mx-auto">
        <div className="h-px bg-gray-100 relative overflow-hidden">
          <div 
            className="absolute top-0 left-0 h-full bg-gray-300 transition-all duration-75 ease-linear"
            style={{
              width: `${((currentIndex + 1) / testimonials.length) * 100}%`
            }}
          />
        </div>
      </div>
    </div>
  );
};

export default TestimonialSlider;