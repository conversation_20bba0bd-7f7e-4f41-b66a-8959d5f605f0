@tailwind base;
@tailwind components;
@tailwind utilities;

/* ULTRA-MINIMAL GLOBALS - ONLY ESSENTIALS */

:root {
  --warm-white: #FEFDF8;
  --charcoal: #3A3432;
  --gold: #C9A961;
  --sage: #7C9885;
  --gray: #6B6B6B;
}

* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  -webkit-font-smoothing: antialiased;
}

body {
  font-family: 'Helvetica Neue', -apple-system, BlinkMacSystemFont, sans-serif;
  font-weight: 300;
  line-height: 1.8;
  color: var(--charcoal);
  background: var(--warm-white);
  overflow-x: hidden;
}

/* TYPOGRAPHY */
h1, h2, h3 {
  font-family: 'Playfair Display', serif;
  font-weight: 400;
  letter-spacing: 0.02em;
  line-height: 1.2;
}

p {
  margin-bottom: 1.5rem;
}

a {
  color: inherit;
  text-decoration: none;
  transition: opacity 0.3s ease;
}

a:hover {
  opacity: 0.6;
}

img {
  max-width: 100%;
  height: auto;
}

/* LAYOUT */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 5%;
}

.section {
  padding: 80px 0;
}

/* COMPONENTS */
.btn {
  padding: 15px 40px;
  border: 1px solid currentColor;
  background: transparent;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  transition: all 0.3s ease;
  cursor: pointer;
  display: inline-block;
}

.btn:hover {
  background: currentColor;
  color: var(--warm-white);
}

/* UTILITIES */
.text-center { text-align: center; }
.mt-8 { margin-top: 2rem; }
.mb-8 { margin-bottom: 2rem; }
.fade-in { 
  animation: fadeIn 0.6s ease-out forwards; 
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

/* MINIMAL SCROLLBAR */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: var(--sage);
  opacity: 0.3;
}