'use client';

import React, { useCallback, useMemo, useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { 
  ArrowRight, 
  Instagram, 
  Facebook, 
  CalendarCheck, 
  Star, 
  Globe, 
  Heart, 
  Sun,
  Waves,
  Mountain,
  Leaf,
  Users,
  Award,
  Clock,
  MapPin,
  Phone,
  Mail,
  Send,
  ChevronDown,
  Quote,
  Flower,
  Play,
  CheckCircle,
  Calendar,
  MessageCircle
} from 'lucide-react';

import { blogPosts } from '@/data/blogPosts';

import TestimonialSlider from '@/components/TestimonialSlider';
import FAQAccordion from '@/components/FAQAccordion';
import RetreatCalendar from '@/components/RetreatCalendar';

// Animation variants for enhanced user experience
const fadeInUp = {
  initial: { opacity: 0, y: 60 },
  animate: { opacity: 1, y: 0 },
  transition: { duration: 0.6, ease: "easeOut" }
};

const staggerChildren = {
  animate: {
    transition: {
      staggerChildren: 0.2
    }
  }
};

const scaleOnHover = {
  whileHover: { scale: 1.05 },
  transition: { duration: 0.3 }
};

// Enhanced SafeIcon component with better prop types and accessibility
const SafeIcon = React.memo(({ Icon, className = '', size = 'default', ...props }) => {
  if (!Icon) return null;
  
  const sizeClasses = {
    small: 'w-4 h-4',
    default: 'w-5 h-5',
    large: 'w-6 h-6',
    xl: 'w-8 h-8'
  };
  
  return (
    <Icon 
      className={`${sizeClasses[size]} ${className}`} 
      {...props}
      role="img"
      aria-hidden="true"
    />
  );
});
SafeIcon.displayName = 'SafeIcon';

// Enhanced floating elements for spiritual atmosphere
const FloatingElement = React.memo(({ children, delay = 0, className = '' }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay, duration: 0.8 }}
      className={`absolute ${className}`}
    >
      {children}
    </motion.div>
  );
});
FloatingElement.displayName = 'FloatingElement';

// Ultimate Premium Hero Section - World-Class Design
const HeroSection = React.memo(() => {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [scrollY, setScrollY] = useState(0);
  const [isLoaded, setIsLoaded] = useState(false);
  const [ambientSound, setAmbientSound] = useState(false);

  const scrollToRetreatSection = useCallback(() => {
    const element = document.getElementById('retreats');
    if (element) {
      element.scrollIntoView({ 
        behavior: 'smooth', 
        block: 'start',
        inline: 'nearest' 
      });
    }
  }, []);

  // Scroll parallax effect
  useEffect(() => {
    const handleScroll = () => {
      setScrollY(window.scrollY);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Subtle parallax mouse movement
  useEffect(() => {
    const handleMouseMove = (e) => {
      setMousePosition({
        x: (e.clientX / window.innerWidth) * 20 - 10,
        y: (e.clientY / window.innerHeight) * 20 - 10
      });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  const heroVariants = {
    hidden: { opacity: 0 },
    visible: { 
      opacity: 1,
      transition: {
        duration: 1.2,
        ease: [0.25, 0.46, 0.45, 0.94],
        staggerChildren: 0.3
      }
    }
  };

  const textVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: {
        duration: 0.8,
        ease: [0.25, 0.46, 0.45, 0.94]
      }
    }
  };

  const badgeVariants = {
    hidden: { opacity: 0, scale: 0.8 },
    visible: { 
      opacity: 1, 
      scale: 1,
      transition: {
        duration: 0.6,
        ease: [0.25, 0.46, 0.45, 0.94]
      }
    }
  };

  return (
    <section className="hero hero-section relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Premium Background with Parallax */}
      <motion.div 
        className="absolute inset-0 w-full h-full hero-image"
        style={{
          transform: `translate3d(${mousePosition.x * 0.5}px, ${(mousePosition.y * 0.5) + (scrollY * 0.5)}px, 0)`,
          top: '-10%',
          height: '110%'
        }}
        transition={{ type: "spring", stiffness: 100, damping: 30 }}
      >
        <Image
          src="/images/background/bali-hero.webp"
          alt="Sacred Bali retreat sanctuary with golden sunrise over ancient temples"
          fill
          priority
          className="object-cover scale-110"
          sizes="100vw"
          quality={95}
          onLoad={() => setIsLoaded(true)}
        />
        
        {/* Sophisticated gradient overlay - JAŚNIEJSZY */}
        <div className="absolute inset-0 bg-gradient-to-b from-black/20 via-transparent to-black/20" />
        
        {/* Cinematic light effects */}
        <div className="absolute inset-0 bg-gradient-to-tr from-transparent via-amber-500/5 to-transparent" />
        
        {/* REMOVED: Particle effects for minimalism */}
      </motion.div>

      {/* Hero Content with Enhanced Typography */}
      <motion.div 
        className="hero-content relative z-10 text-center px-6 max-w-6xl mx-auto"
        variants={heroVariants}
        initial="hidden"
        animate={isLoaded ? "visible" : "hidden"}
      >
        {/* Premium Badge */}
        <motion.div variants={badgeVariants} className="mb-8">
          <div className="inline-flex items-center space-x-3 bg-white/5 backdrop-blur-xl px-8 py-4 border border-white/20">
            <div className="w-2 h-2 bg-amber-400 rounded-full animate-pulse"></div>
            <span className="text-sm font-light text-white tracking-[0.2em] uppercase">
              Certified Yoga Alliance RYT-500 • Est. 2019
            </span>
          </div>
        </motion.div>

        {/* Main Title */}
        <motion.h1 
          variants={textVariants}
          className="hero-title font-serif mb-6"
        >
          BAKASANA
        </motion.h1>

        {/* Subtitle */}
        <motion.p 
          variants={textVariants}
          className="hero-subtitle mb-8"
        >
          Transformative Wellness Retreats
        </motion.p>

        {/* Location & Experience Info - SIMPLIFIED */}
        <motion.div 
          variants={textVariants}
          className="flex flex-wrap items-center justify-center gap-8 mb-12 text-white/80"
        >
          <span className="text-sm font-light tracking-wide">Bali • Sri Lanka</span>
          <div className="w-1 h-1 bg-white/40 rounded-full"></div>
          <span className="text-sm font-light tracking-wide">Max 12 People</span>
          <div className="w-1 h-1 bg-white/40 rounded-full"></div>
          <span className="text-sm font-light tracking-wide">7-14 Days</span>
        </motion.div>

        {/* Inspirational Quote - MORE EMOTIONAL */}
        <motion.blockquote 
          variants={textVariants}
          className="text-white/90 mb-16 max-w-3xl mx-auto font-light leading-relaxed text-lg italic"
        >
          „Pozwól sobie na moment zatrzymania. 
          Odkryj kobietę, która zawsze w Tobie była - silną, spokojną, piękną."
        </motion.blockquote>

        {/* CTA Buttons */}
        <motion.div 
          variants={textVariants}
          className="flex flex-col sm:flex-row gap-6 justify-center items-center mb-20"
        >
          <motion.button
            onClick={scrollToRetreatSection}
            className="btn-primary group relative overflow-hidden px-8 py-4"
            whileHover={{ 
              scale: 1.02,
              boxShadow: '0 10px 40px rgba(0,0,0,0.2)'
            }}
            whileTap={{ scale: 0.98 }}
          >
            <span className="relative z-10 flex items-center font-medium tracking-wide cta-text">
              Odkryj Swoją Transformację
              <ArrowRight className="ml-3 w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" />
            </span>
          </motion.button>

          <motion.a
            href="#video"
            className="btn-outline group flex items-center space-x-3 px-6 py-4"
            whileHover={{ 
              scale: 1.02,
              backgroundColor: 'rgba(255,255,255,0.1)'
            }}
            whileTap={{ scale: 0.98 }}
          >
            <Play className="w-5 h-5" />
            <span className="font-light tracking-wide">Zobacz Film</span>
          </motion.a>
        </motion.div>

        {/* Trust Indicators - SIMPLIFIED */}
        <motion.div 
          variants={textVariants}
          className="flex flex-wrap justify-center items-center gap-8 opacity-90"
        >
          <span className="text-white text-sm font-light">4.9/5 Rating</span>
          <div className="w-1 h-1 bg-white/40 rounded-full"></div>
          <span className="text-white text-sm font-light">200+ Certified</span>
          <div className="w-1 h-1 bg-white/40 rounded-full"></div>
          <span className="text-white text-sm font-light">500+ Transformations</span>
        </motion.div>
      </motion.div>

      {/* Premium Scroll Indicator */}
      <motion.div
        className="scroll-indicator absolute bottom-8 left-1/2 transform -translate-x-1/2 cursor-pointer group"
        animate={{ y: [0, 12, 0], opacity: 1 }}
        transition={{ y: { duration: 2.5, repeat: Infinity, ease: "easeInOut" }, opacity: { delay: 2, duration: 1 } }}
        onClick={scrollToRetreatSection}
        whileHover={{ scale: 1.1 }}
        initial={{ opacity: 0 }}
      >
        <div className="flex flex-col items-center space-y-3">
          <div className="w-6 h-12 border-2 border-white/30 rounded-full relative group-hover:border-white/50 transition-colors">
            <motion.div
              className="w-1 h-3 bg-white/70 rounded-full absolute left-1/2 top-2 transform -translate-x-1/2"
              animate={{ y: [0, 16, 0], opacity: [1, 0, 1] }}
              transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
            />
          </div>
          <div className="text-center">
            <span className="text-white/60 text-xs uppercase tracking-[0.2em] font-light">
              Odkryj więcej
            </span>
            <ChevronDown className="w-5 h-5 text-white/70 mx-auto mt-1 group-hover:text-white transition-colors" />
          </div>
        </div>
      </motion.div>

      {/* REMOVED: Ambient Sound Toggle for minimalism */}


    </section>
  );
});
HeroSection.displayName = 'HeroSection';

// Enhanced Premium Card Component with micro-interactions
const PremiumCard = React.memo(({ 
  title, 
  description, 
  link, 
  imageUrl, 
  price, 
  location, 
  duration, 
  highlights = [],
  badge,
  className = '' 
}) => {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <motion.div 
      className={`premium-card group relative overflow-hidden ${className}`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      variants={fadeInUp}
      whileHover={scaleOnHover}
    >
      {/* Enhanced Image with Overlay */}
      {imageUrl && (
        <div className="card-image relative overflow-hidden">
          <Image
            src={imageUrl}
            alt={title}
            fill
            className="object-cover transition-transform duration-700 group-hover:scale-110"
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            quality={90}
          />
          
          {/* Premium gradient overlay */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent" />
          
          {/* Badge */}
          {badge && (
            <div className="absolute top-4 right-4 bg-gold text-black px-4 py-2 text-xs font-light tracking-wider uppercase">
              {badge}
            </div>
          )}
          
          {/* Quick info overlay */}
          <div className="absolute bottom-4 left-4 right-4 text-white">
            <div className="flex items-center justify-between">
              {location && (
                <div className="flex items-center space-x-1">
                  <MapPin className="w-3 h-3" />
                  <span className="text-xs">{location}</span>
                </div>
              )}
              {duration && (
                <div className="flex items-center space-x-1">
                  <Clock className="w-3 h-3" />
                  <span className="text-xs">{duration}</span>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Enhanced Content */}
      <div className="card-content p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-xl font-light">
            {link ? (
              <Link 
                href={link} 
                className="hover:text-accent transition-colors duration-300"
              >
                {title}
              </Link>
            ) : (
              title
            )}
          </h3>
          
          {price && (
            <div className="text-right">
              <span className="text-accent font-medium">{price}</span>
              <div className="text-xs text-gray-500">per person</div>
            </div>
          )}
        </div>

        <p className="text-gray-600 mb-6 leading-relaxed">
          {description}
        </p>

        {/* Highlights */}
        {highlights.length > 0 && (
          <div className="mb-6">
            <ul className="space-y-2">
              {highlights.map((highlight, index) => (
                <li key={index} className="flex items-start space-x-2 text-sm">
                  <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span className="text-gray-600">{highlight}</span>
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* Enhanced CTA */}
        {link && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: isHovered ? 1 : 0.7, y: isHovered ? 0 : 10 }}
            transition={{ duration: 0.3 }}
          >
            <Link
              href={link}
              className="premium-cta group/cta inline-flex items-center space-x-2 text-accent hover:text-accent-dark transition-colors duration-300"
            >
              <span className="font-medium">Dowiedz się więcej</span>
              <ArrowRight className="w-4 h-4 group-hover/cta:translate-x-1 transition-transform" />
            </Link>
          </motion.div>
        )}
      </div>
    </motion.div>
  );
});
PremiumCard.displayName = 'PremiumCard';

// Enhanced Section Divider with spiritual elements
const SectionDivider = React.memo(({ variant = 'default' }) => {
  const variants = {
    default: 'divider-line',
    lotus: 'divider-lotus',
    gradient: 'divider-gradient'
  };

  return (
    <motion.div 
      className={`section-divider ${variants[variant]}`}
      initial={{ opacity: 0, scaleX: 0 }}
      whileInView={{ opacity: 1, scaleX: 1 }}
      viewport={{ once: true }}
      transition={{ duration: 0.8 }}
    >
      {variant === 'lotus' && (
        <div className="flex items-center justify-center">
          <div className="w-full h-px bg-gradient-to-r from-transparent via-accent to-transparent" />
          <div className="mx-4 p-2 bg-white rounded-full">
            <Flower className="w-6 h-6 text-accent" />
          </div>
          <div className="w-full h-px bg-gradient-to-r from-accent via-accent to-transparent" />
        </div>
      )}
    </motion.div>
  );
});
SectionDivider.displayName = 'SectionDivider';

// Enhanced Stats Section
const StatsSection = React.memo(() => {
  const stats = [
    { icon: Users, value: '500+', label: 'Szczęśliwych Uczestniczek' },
    { icon: Star, value: '4.9/5', label: 'Średnia Ocena' },
    { icon: Globe, value: '15+', label: 'Krajów' },
    { icon: Award, value: '8+', label: 'Lat Doświadczenia' }
  ];

  return (
    <section className="stats-section py-16 bg-gradient-to-r from-accent/5 to-accent/10">
      <div className="container-unified">
        <motion.div 
          className="grid grid-cols-2 md:grid-cols-4 gap-8"
          initial="initial"
          whileInView="animate"
          viewport={{ once: true }}
          variants={staggerChildren}
        >
          {stats.map((stat, index) => (
            <motion.div
              key={index}
              variants={fadeInUp}
              className="text-center group"
            >
              <motion.div
                className="inline-flex items-center justify-center w-16 h-16 bg-white rounded-full shadow-lg mb-4 group-hover:bg-accent group-hover:text-white transition-colors duration-300"
                whileHover={{ scale: 1.1 }}
              >
                <stat.icon className="w-8 h-8" />
              </motion.div>
              <div className="text-3xl font-light text-gray-800 mb-2">{stat.value}</div>
              <div className="text-sm text-gray-600">{stat.label}</div>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  );
});
StatsSection.displayName = 'StatsSection';

// Enhanced Newsletter Section
const NewsletterSection = React.memo(() => {
  const [email, setEmail] = useState('');
  const [isSubscribed, setIsSubscribed] = useState(false);

  const handleSubmit = useCallback((e) => {
    e.preventDefault();
    // Handle newsletter subscription
    setIsSubscribed(true);
    setEmail('');
  }, []);

  return (
    <section className="newsletter-section py-16 bg-gradient-to-r from-accent to-accent-dark">
      <div className="container-unified">
        <motion.div 
          className="max-w-2xl mx-auto text-center text-white"
          initial="initial"
          whileInView="animate"
          viewport={{ once: true }}
          variants={staggerChildren}
        >
          <motion.div variants={fadeInUp} className="mb-6">
            <Flower className="w-12 h-12 mx-auto mb-4 text-yellow-300" />
            <h2 className="text-3xl font-light mb-4">
              Dołącz do Naszej Duchowej Społeczności
            </h2>
            <p className="text-lg opacity-90">
              Otrzymuj inspirujące treści, medytacje i pierwsze informacje o nowych retreatach
            </p>
          </motion.div>

          <motion.form 
            variants={fadeInUp}
            onSubmit={handleSubmit}
            className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto"
          >
            <input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="Twój adres email"
              className="flex-1 px-4 py-3 rounded-full text-gray-800 focus:outline-none focus:ring-2 focus:ring-yellow-300"
              required
            />
            <motion.button
              type="submit"
              className="btn-secondary bg-yellow-400 text-gray-800 hover:bg-yellow-300"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              disabled={isSubscribed}
            >
              {isSubscribed ? (
                <>
                  <CheckCircle className="w-4 h-4 mr-2" />
                  Zapisano!
                </>
              ) : (
                <>
                  <Send className="w-4 h-4 mr-2" />
                  Zapisz się
                </>
              )}
            </motion.button>
          </motion.form>

          {isSubscribed && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="mt-4 text-yellow-300"
            >
              Dziękujemy! Wkrótce otrzymasz od nas wiadomość ✨
            </motion.div>
          )}
        </motion.div>
      </div>
    </section>
  );
});
NewsletterSection.displayName = 'NewsletterSection';

// Main WellnessPage Component - Enhanced Enterprise Version
const WellnessPage = ({ latestPosts }) => {
  const router = useRouter();
  const posts = useMemo(() => latestPosts || blogPosts.slice(0, 3), [latestPosts]);

  // Enhanced retreat highlights with more detailed information
  const retreatHighlights = useMemo(() => [
    {
      id: 'ubud',
      title: 'Sanktuarium Ubud',
      description: 'Siedem dni duchowego zanurzenia w kulturowym sercu Bali. Odkryj swoją wewnętrzną mądrość wśród starożytnych tarasów ryżowych i świętych świątyń.',
      imageUrl: '/images/retreats/ubud.webp',
      location: 'Ubud, Bali',
      duration: '7 dni',
      price: '€2,400',
      badge: 'Popularne',
      highlights: [
        'Codzienna joga i medytacja',
        'Wycieczki do świętych świątyń',
        'Warsztaty z filozofii jogi',
        'Kąpiele w górskich źródłach',
        'Certyfikowane zakwaterowanie'
      ]
    },
    {
      id: 'gili-air',
      title: 'Raj Gili Air',
      description: 'Pięć dni czystego spokoju na rajskiej wyspie, gdzie czas płynie wolno, a pokój przychodzi naturalnie. Odnowa w towarzystwie inspirujących kobiet.',
      imageUrl: '/images/retreats/gili.webp',
      location: 'Gili Air, Indonezja',
      duration: '5 dni',
      price: '€1,800',
      badge: 'Ekskluzywne',
      highlights: [
        'Plaża prywatna tylko dla grupy',
        'Snorkeling z żółwiami',
        'Yoga na wschodzie słońca',
        'Masaże balijskie',
        'Maksymalnie 6 osób'
      ]
    },
    {
      id: 'canggu',
      title: 'Klify Canggu',
      description: 'Dziesięć dni transformacji z sesjami jogi na klifach z widokiem na nieskończony ocean. Spektakularne zachody słońca i wewnętrzna przemiana.',
      imageUrl: '/images/retreats/canggu.webp',
      location: 'Canggu, Bali',
      duration: '10 dni',
      price: '€3,200',
      badge: 'Intensywne',
      highlights: [
        'Joga na klifach o zachodzie',
        'Warsztaty transformacji',
        'Surfing dla początkujących',
        'Ceremonie pełni księżyca',
        'Indywidualne sesje coaching'
      ]
    },
    {
      id: 'sri-lanka',
      title: 'Perła Sri Lanka',
      description: 'Nowy wymiar duchowości w perle Oceanu Indyjskiego. Odkryj starożytną mądrość Ajurwedy i buddyjskich praktyk w otoczeniu dziewiczej przyrody.',
      imageUrl: '/images/retreats/sri-lanka.webp',
      location: 'Ella, Sri Lanka',
      duration: '8 dni',
      price: '€2,800',
      badge: 'Nowość',
      highlights: [
        'Authentic Ayurvedic treatments',
        'Buddhist meditation sessions',
        'Tea plantation visits',
        'Elephant sanctuary experience',
        'Traditional Sri Lankan cuisine'
      ]
    }
  ], []);

  // Enhanced testimonials with more details
  const testimonials = useMemo(() => [
    {
      quote: "To była najbardziej transformująca podróż mojego życia. Julia stworzyła przestrzeń ciepła i bezpieczeństwa, która pozwoliła mi prawdziwie połączyć się z sobą. Wróciłam jako zupełnie nowa kobieta, pełna pewności siebie i wewnętrznego spokoju.",
      author: "Anna Kowalska",
      location: "Warszawa",
      rating: 5,
      retreat: "Sanktuarium Ubud",
      avatar: "/images/testimonials/anna.webp"
    },
    {
      quote: "Idealny balans jogi, kultury i relaksu. Julia ma dar tworzenia magicznych chwil, które zostają w pamięci na zawsze. Nasza grupa stała się jak druga rodzina - kobiety wspierające się nawzajem w drodze do siebie.",
      author: "Katarzyna Nowak",
      location: "Gdańsk",
      rating: 5,
      retreat: "Raj Gili Air",
      avatar: "/images/testimonials/kasia.webp"
    },
    {
      quote: "Każdy dzień przynosił nowe odkrycia i głębsze zrozumienie siebie. Julia prowadzi z taką mądrością i empatią, że czujesz się bezpiecznie na każdym kroku. To nie był tylko retreat - to była prawdziwa podróż do siebie.",
      author: "Marta Wiśniewska",
      location: "Wrocław",
      rating: 5,
      retreat: "Klify Canggu",
      avatar: "/images/testimonials/marta.webp"
    },
    {
      quote: "Bali z Julią to nie tylko joga - to odkrywanie swojej wewnętrznej siły i potencjału. Wróciłam z nową energią, spokojem wewnętrznym i pewnością, że mogę wszystko. Polecam każdej kobiecie, która szuka prawdziwej zmiany.",
      author: "Agnieszka Zielińska",
      location: "Kraków",
      rating: 5,
      retreat: "Sanktuarium Ubud",
      avatar: "/images/testimonials/agnieszka.webp"
    }
  ], []);

  // Enhanced FAQs with more comprehensive answers
  const faqs = useMemo(() => [
    {
      question: "Czy retreaty są odpowiednie dla początkujących w jodze?",
      answer: "Absolutnie tak! Nasze retreaty przyjmują kobiety na każdym poziomie doświadczenia. Julia prowadzi każdą sesję z uwagą na indywidualne potrzeby i możliwości. Oferujemy modyfikacje dla każdego ćwiczenia, więc każda znajdzie swoje miejsce w naszym kręgu, niezależnie od poziomu zaawansowania."
    },
    {
      question: "Co dokładnie jest wliczone w cenę retreatu?",
      answer: "Cena obejmuje pełne zakwaterowanie w starannie wybranych miejscach, wszystkie wegetariańskie/wegańskie posiłki przygotowane z lokalnych składników, codzienne sesje jogi i medytacji, wycieczki kulturalne z przewodnikiem, transfery lotniskowe, ubezpieczenie grupowe oraz wsparcie Julii przez całą podróż. Jedyne dodatkowe koszty to loty i ewentualne zakupy osobiste."
    },
    {
      question: "Jakie są terminy najbliższych retreatów?",
      answer: "Nasze nadchodzące retreaty zaplanowane są na: czerwiec 2024 (Ubud), lipiec 2024 (Gili Air), wrzesień 2024 (Canggu) oraz październik 2024 (Sri Lanka - nowość!). Szczegółowe daty i dostępność miejsc znajdziesz w sekcji kalendarza. Rezerwacja miejsc odbywa się z wyprzedzeniem 3-6 miesięcy."
    },
    {
      question: "Czy muszę mieć wcześniejsze doświadczenie w medytacji?",
      answer: "Nie, żadne wcześniejsze doświadczenie nie jest konieczne. Nasze retreaty są idealne zarówno dla osób rozpoczynających swoją duchową podróż, jak i dla bardziej zaawansowanych praktyków. Julia wprowadza techniki medytacji stopniowo, zawsze dostosowując je do grupy i indywidualnych potrzeb każdej uczestniczki."
    },
    {
      question: "Jak duże są grupy na retreatach?",
      answer: "Nasze grupy są świadomie małe i intymne - zazwyczaj 6-12 uczestniczek, maksymalnie 15 osób. To zapewnia indywidualną uwagę Julii, możliwość nawiązania głębokich, znaczących połączeń z innymi kobietami oraz stworzenie bezpiecznej przestrzeni dla osobistych przemian."
    },
    {
      question: "Jakie są warunki pogodowe i co spakować?",
      answer: "Bali i Sri Lanka cieszą się tropikalnym klimatem przez cały rok. Temperatura wynosi 26-32°C, z możliwością opadów (szczególnie październik-marzec). Przygotowujemy szczegółową listę rzeczy do spakowania dla każdej uczestniczki, uwzględniającą specyfikę konkretnego retreatu i pory roku."
    }
  ], []);

  // Enhanced retreat schedule with more details
  const retreats = useMemo(() => [
    {
      id: 'ubud-june-2024',
      type: 'Transformacja Życia',
      title: 'Sanktuarium Ubud',
      startDate: '15 czerwca 2024',
      endDate: '22 czerwca 2024',
      location: 'Ubud, Bali',
      participants: 8,
      maxParticipants: 12,
      price: '€2,400',
      originalPrice: '€2,800',
      description: 'Odkryj swoją wewnętrzną siłę w duchowym sercu Bali. Julia osobiście prowadzi każdą sesję w otoczeniu starożytnych świątyń i tarasów ryżowych.',
      available: true,
      status: 'early-bird',
      highlights: ['Codzienna joga i medytacja', 'Wycieczki do świątyń', 'Warsztaty filozofii jogi'],
      accommodation: '4* eco-resort',
      meals: 'Wegetariańskie/wegańskie',
      activities: ['Wycieczki do świątyń', 'Spacery po tarasach ryżowych', 'Tradycyjne targi', 'Terapie uzdrawiające']
    },
    {
      id: 'gili-air-july-2024',
      type: 'Odnowa Duszy',
      title: 'Raj Gili Air',
      startDate: '20 lipca 2024',
      endDate: '25 lipca 2024',
      location: 'Gili Air, Indonezja',
      participants: 5,
      maxParticipants: 8,
      price: '€1,800',
      description: 'Pięć dni czystego spokoju na rajskiej wyspie. Idealne miejsce na reset i głęboką odnowę w małej, intymnej grupie.',
      available: true,
      status: 'filling-fast',
      highlights: ['Snorkeling z żółwiami', 'Yoga na plaży', 'Masaże balijskie'],
      accommodation: 'Beachfront bungalows',
      meals: 'Fresh seafood & vegetarian',
      activities: ['Snorkeling z koralowcami', 'Joga o zachodzie słońca', 'Wycieczki rowerowe', 'Medytacja na plaży']
    },
    {
      id: 'canggu-september-2024',
      type: 'Intensywna Przemiana',
      title: 'Klify Canggu',
      startDate: '10 września 2024',
      endDate: '20 września 2024',
      location: 'Canggu, Bali',
      participants: 10,
      maxParticipants: 15,
      price: '€3,200',
      description: 'Dziesięć dni głębokiej transformacji z jogą na klifach i spektakularnymi zachodami słońca nad oceanem.',
      available: true,
      status: 'confirmed',
      highlights: ['Joga na klifach', 'Surfing lessons', 'Ceremonie księżyca'],
      accommodation: 'Luxury cliff-top villa',
      meals: 'Gourmet vegetarian cuisine',
      activities: ['Joga na klifach', 'Nauka surfingu', 'Wizyty przy wodospadach', 'Święte ceremonie']
    },
    {
      id: 'sri-lanka-october-2024',
      type: 'Ayurveda & Mindfulness',
      title: 'Perła Sri Lanka',
      startDate: '15 października 2024',
      endDate: '23 października 2024',
      location: 'Ella, Sri Lanka',
      participants: 0,
      maxParticipants: 10,
      price: '€2,800',
      description: 'Nowy wymiar duchowości w perle Oceanu Indyjskiego. Odkryj starożytną mądrość Ajurwedy i buddyjskich praktyk.',
      available: true,
      status: 'new',
      highlights: ['Authentic Ayurveda', 'Buddhist meditation', 'Tea plantation visits'],
      accommodation: 'Boutique mountain resort',
      meals: 'Traditional Sri Lankan & Ayurvedic',
      activities: ['Terapie ajurwedyjskie', 'Wizyty w świątyniach', 'Sanktuarium słoni', 'Podróże koleją']
    }
  ], []);

  // Enhanced social links with more platforms
  const socialLinks = useMemo(() => [
    { 
      id: 'instagram', 
      href: 'https://www.instagram.com/fly_with_bakasana', 
      label: 'Instagram', 
      icon: Instagram,
      description: 'Codzienne inspiracje i zdjęcia z naszych podróży'
    },
    { 
      id: 'facebook', 
      href: 'https://www.facebook.com/p/Fly-with-bakasana-100077568306563/', 
      label: 'Facebook', 
      icon: Facebook,
      description: 'Dołącz do naszej społeczności na Facebooku'
    },
    { 
      id: 'bookings', 
      href: 'https://app.fitssey.com/Flywithbakasana/frontoffice', 
      label: 'Rezerwacje', 
      icon: CalendarCheck,
      description: 'Zarezerwuj swoje miejsce na retreatach'
    },
    {
      id: 'whatsapp',
      href: 'https://wa.me/48123456789',
      label: 'WhatsApp',
      icon: MessageCircle,
      description: 'Bezpośredni kontakt z Julią'
    }
  ], []);

  return (
    <div className="wellness-page bg-gradient-to-b from-white to-gray-50">
      <HeroSection />
      
      <StatsSection />

      {/* Enhanced Retreat Highlights Section */}
      <section id="retreats" className="section-padding">
        <div className="container-unified">
          <motion.div 
            className="section-unified-title"
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            variants={staggerChildren}
          >
            <motion.div variants={fadeInUp} className="text-center mb-4">
              <Flower className="w-8 h-8 text-accent mx-auto mb-4" />
            </motion.div>
            
            <motion.h2 variants={fadeInUp} className="text-4xl font-light mb-6">
              Nasze Retreaty
            </motion.h2>
            
            <motion.p variants={fadeInUp} className="cta-text max-w-3xl mx-auto mb-8">
              Odkryj transformacyjną moc jogi w najświętszych i najpiękniejszych miejscach Azji.
              Każdy retreat to carefully crafted journey designed to nurture your soul and awaken your inner wisdom.
            </motion.p>
            
            <motion.div variants={fadeInUp} className="community-text">
              <Quote className="w-6 h-6 text-accent mx-auto mb-2" />
              "Każda podróż zaczyna się od jednego kroku. Pozwól sobie na tę magię transformacji."
            </motion.div>
          </motion.div>

          <motion.div 
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-8 mt-16"
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            variants={staggerChildren}
          >
            {retreatHighlights.map((retreat) => (
              <PremiumCard
                key={retreat.id}
                title={retreat.title}
                description={retreat.description}
                imageUrl={retreat.imageUrl}
                link={`/program/${retreat.id}`}
                location={retreat.location}
                duration={retreat.duration}
                price={retreat.price}
                badge={retreat.badge}
                highlights={retreat.highlights}
                className="md:col-span-1"
              />
            ))}
          </motion.div>
        </div>
      </section>

      <SectionDivider variant="lotus" />

      {/* Enhanced About Julia Section */}
      <section className="section-padding bg-white">
        <div className="container-unified">
          <motion.div 
            className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center"
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            variants={staggerChildren}
          >
            <motion.div variants={fadeInUp} className="relative">
              <div className="aspect-[4/5] relative overflow-hidden rounded-2xl">
                <Image
                  src="/images/profile/omnie-opt.webp"
                  alt="Julia Jakubowicz - Certified Yoga Instructor & Spiritual Guide"
                  fill
                  className="object-cover"
                  sizes="(max-width: 1024px) 100vw, 50vw"
                  quality={95}
                />
              </div>
              
              {/* Floating certification badges */}
              <div className="absolute -top-4 -right-4 bg-white p-3 rounded-full shadow-lg">
                <Award className="w-6 h-6 text-accent" />
              </div>
              <div className="absolute -bottom-4 -left-4 bg-accent text-white p-3 rounded-full shadow-lg">
                <Flower className="w-6 h-6" />
              </div>
            </motion.div>

            <motion.div variants={fadeInUp} className="space-y-6">
              <h2 className="text-4xl font-light mb-8">Poznaj Julię</h2>

              <blockquote className="julia-quote bg-accent/5 p-6 rounded-xl border-l-4 border-accent">
                <Quote className="w-6 h-6 text-accent mb-4" />
                <p className="text-lg leading-relaxed mb-4">
                  "Prawdziwa praktyka zaczyna się, gdy schodzimy z maty. To tam, w codziennym życiu, 
                  odkrywamy swoją prawdziwą siłę i mądrość, która była w nas przez cały czas."
                </p>
                <cite className="text-accent font-medium">
                  — Julia Jakubowicz, RYT 500, Instruktorka Jogi & Przewodniczka Duchowa
                </cite>
              </blockquote>

              <p className="intro-text text-lg leading-relaxed">
                Certyfikowana instruktorka jogi z 8-letnim doświadczeniem i pasją dzielenia się 
                mądrością starożytnych praktyk. Julia ukończyła studia w prestiżowej szkole jogi 
                w Rishikesh, Indie, oraz pogłębia swoją wiedzę podczas regularnych pobytów w Azji.
              </p>

              <p className="leading-relaxed">
                Specjalizuje się w Hatha i Vinyasa Flow, praktykach medytacyjnych oraz filozofii jogi. 
                Bali i Sri Lanka stały się jej drugimi duchowymi domami, miejscami, gdzie prowadzi 
                kobiety w ich transformacyjnych podróżach do siebie.
              </p>

              <div className="community-text bg-gradient-to-r from-accent/10 to-accent/5 p-4 rounded-xl">
                <Heart className="w-5 h-5 text-accent inline mr-2" />
                "Każda kobieta nosi w sobie niesamowitą siłę i mądrość. Moją misją jest pomóc jej 
                to odkryć i nauczyć się żyć w pełni autentycznie."
              </div>

              {/* Enhanced certifications */}
              <div className="space-y-4">
                <h3 className="text-xl font-medium">Certyfikacje & Kwalifikacje</h3>
                <div className="grid grid-cols-2 gap-3">
                  {[
                    { name: 'RYT 500', description: 'Yoga Alliance Certified' },
                    { name: 'Yin Yoga', description: 'Advanced Training' },
                    { name: 'Aerial Yoga', description: 'Certified Instructor' },
                    { name: 'MBSR', description: 'Mindfulness Based Stress Reduction' },
                    { name: 'Ayurveda', description: 'Holistic Wellness' },
                    { name: 'Meditation', description: 'Tibetan & Vipassana' }
                  ].map((cert) => (
                    <div key={cert.name} className="p-3 bg-white border border-accent/20 rounded-lg hover:border-accent/40 transition-colors">
                      <div className="font-medium text-accent">{cert.name}</div>
                      <div className="text-sm text-gray-600">{cert.description}</div>
                    </div>
                  ))}
                </div>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </section>

      <SectionDivider />

      {/* Enhanced Testimonials Section */}
      <section className="section-padding">
        <div className="container-unified">
          <motion.div 
            className="section-unified-title"
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            variants={staggerChildren}
          >
            <motion.h2 variants={fadeInUp} className="text-4xl font-light mb-6">
              Głosy Naszej Społeczności
            </motion.h2>
            <motion.p variants={fadeInUp} className="cta-text max-w-2xl mx-auto">
              Przeczytaj autentyczne historie transformacji od kobiet, które odważyły się 
              rozpocząć swoją duchową podróż z nami.
            </motion.p>
          </motion.div>

          <TestimonialSlider testimonials={testimonials} />
        </div>
      </section>

      <SectionDivider variant="lotus" />

      {/* Enhanced Upcoming Retreats Section */}
      <section className="section-padding bg-gradient-to-b from-gray-50 to-white">
        <div className="container-unified">
          <motion.div 
            className="section-unified-title"
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            variants={staggerChildren}
          >
            <motion.h2 variants={fadeInUp} className="text-4xl font-light mb-6">
              Nadchodzące Retreaty
            </motion.h2>
            <motion.p variants={fadeInUp} className="cta-text max-w-2xl mx-auto mb-8">
              Wybierz idealny retreat dla swojej podróży transformacji. Wszystkie daty, 
              szczegóły i dostępność miejsc w jednym miejscu.
            </motion.p>
            <motion.div variants={fadeInUp} className="exclusivity-badge inline-flex items-center space-x-2 bg-accent/10 px-4 py-2 rounded-full">
              <Star className="w-4 h-4 text-accent" />
              <span className="text-accent font-medium">Ograniczona liczba miejsc • Julia osobiście prowadzi każdy retreat</span>
            </motion.div>
          </motion.div>

          <RetreatCalendar retreats={retreats} />
        </div>
      </section>

      <SectionDivider />

      {/* Enhanced FAQ Section */}
      <section className="section-padding">
        <div className="container-unified">
          <motion.div 
            className="section-unified-title"
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            variants={staggerChildren}
          >
            <motion.h2 variants={fadeInUp} className="text-4xl font-light mb-6">
              Najczęściej Zadawane Pytania
            </motion.h2>
            <motion.p variants={fadeInUp} className="cta-text max-w-2xl mx-auto">
              Masz pytania? To całkowicie naturalne! Każda podróż transformacji zaczyna się 
              od ciekawości i chęci poznania. Znajdź odpowiedzi na najważniejsze pytania.
            </motion.p>
          </motion.div>

          <FAQAccordion faqs={faqs} />
        </div>
      </section>

      <NewsletterSection />

      <SectionDivider variant="lotus" />

      {/* Enhanced Contact Section */}
      <section className="section-padding bg-white">
        <div className="container-unified">
          <motion.div 
            className="section-unified-title"
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            variants={staggerChildren}
          >
            <motion.h2 variants={fadeInUp} className="text-4xl font-light mb-6">
              Skontaktuj się z nami
            </motion.h2>
            <motion.p variants={fadeInUp} className="cta-text max-w-2xl mx-auto mb-12">
              Gotowa na transformację? Masz pytania? Chcesz dowiedzieć się więcej? 
              Skontaktuj się z nami - odpowiemy na każde pytanie z sercem.
            </motion.p>
          </motion.div>

          <motion.div 
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12"
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            variants={staggerChildren}
          >
            {socialLinks.map((link) => (
              <motion.a
                key={link.id}
                href={link.href}
                target="_blank"
                rel="noopener noreferrer"
                variants={fadeInUp}
                className="social-card group p-6 bg-white border border-gray-200 rounded-xl hover:border-accent/40 hover:shadow-lg transition-all duration-300"
                whileHover={{ scale: 1.05 }}
              >
                <div className="flex flex-col items-center text-center space-y-3">
                  <div className="w-12 h-12 bg-accent/10 rounded-full flex items-center justify-center group-hover:bg-accent/20 transition-colors">
                    <SafeIcon Icon={link.icon} className="w-6 h-6 text-accent" />
                  </div>
                  <h3 className="font-medium text-gray-800">{link.label}</h3>
                  <p className="text-sm text-gray-600">{link.description}</p>
                </div>
              </motion.a>
            ))}
          </motion.div>

          <motion.div 
            className="text-center"
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            variants={fadeInUp}
          >
            <div className="inline-flex items-center space-x-2 text-accent mb-4">
              <Flower className="w-5 h-5" />
              <span className="font-light italic text-lg">Namaste</span>
              <Flower className="w-5 h-5" />
            </div>
            <p className="text-gray-600 max-w-md mx-auto">
              "Światło we mnie honoruje światło w tobie. Razem tworzymy piękną podróż transformacji."
            </p>
          </motion.div>
        </div>
      </section>

      <SectionDivider />

      {/* Enhanced Blog Section */}
      <section className="section-padding bg-gradient-to-b from-white to-gray-50">
        <div className="container-unified">
          <motion.div 
            className="section-unified-title"
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            variants={staggerChildren}
          >
            <motion.h2 variants={fadeInUp} className="text-4xl font-light mb-6">
              Nasz Dziennik Podróży
            </motion.h2>
            <motion.p variants={fadeInUp} className="cta-text max-w-2xl mx-auto mb-12">
              Odkryj inspirujące historie, praktyczne wskazówki i głębokie refleksje z naszych 
              duchowych podróży. Każdy artykuł to zaproszenie do głębszego zrozumienia siebie.
            </motion.p>
          </motion.div>

          <motion.div 
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            variants={staggerChildren}
          >
            {posts.map((post) => (
              <PremiumCard
                key={post.id}
                title={post.title}
                description={post.excerpt}
                link={`/blog/${post.slug}`}
                imageUrl={post.image}
                className="blog-card"
              />
            ))}
          </motion.div>

          <motion.div 
            className="text-center mt-12"
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            variants={fadeInUp}
          >
            <motion.div whileHover={{ scale: 1.05 }}>
              <Link
                href="/blog"
                className="btn-primary inline-flex items-center space-x-2"
              >
                <span>Zobacz Wszystkie Artykuły</span>
                <ArrowRight className="w-4 h-4" />
              </Link>
            </motion.div>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default WellnessPage;